import mongoose from 'mongoose';
const { Schema } = mongoose;

// Testimonial schema for testimonials section
const TestimonialSchema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: [100, 'Name cannot exceed 100 characters']
  },
  comment: {
    type: String,
    required: true,
    trim: true,
    maxlength: [1000, 'Comment cannot exceed 1000 characters']
  }
}, { _id: true });

// Island page schema
const IslandPageSchema = new Schema({
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: [200, 'Title cannot exceed 200 characters']
  },
  body1: {
    type: String,
    required: true,
    trim: true,
    maxlength: [5000, 'Body1 cannot exceed 5000 characters']
  },
  body2: {
    type: String,
    required: true,
    trim: true,
    maxlength: [5000, 'Body2 cannot exceed 5000 characters']
  }
}, { _id: false });

// Experiences page schema
const ExperiencesPageSchema = new Schema({
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: [200, 'Title cannot exceed 200 characters']
  },
  body1: {
    type: String,
    required: true,
    trim: true,
    maxlength: [5000, 'Body1 cannot exceed 5000 characters']
  },
  body2: {
    type: String,
    required: true,
    trim: true,
    maxlength: [5000, 'Body2 cannot exceed 5000 characters']
  }
}, { _id: false });

// Testimonials page schema
const TestimonialsPageSchema = new Schema({
  testimonials: {
    type: [TestimonialSchema],
    default: [],
    validate: [
      {
        validator: function(v) {
          return v.length <= 20; // Maximum 20 testimonials
        },
        message: 'Cannot have more than 20 testimonials'
      },
      {
        validator: function(v) {
          // Check for unique names within testimonials
          const names = v.map(t => t.name.toLowerCase());
          return names.length === new Set(names).size;
        },
        message: 'Testimonial names must be unique'
      }
    ]
  }
}, { _id: false });

// Location and contacts page schema
const LocationAndContactsPageSchema = new Schema({
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: [200, 'Title cannot exceed 200 characters']
  },
  body: {
    type: String,
    required: true,
    trim: true,
    maxlength: [5000, 'Body cannot exceed 5000 characters']
  },
  image: {
    type: String,
    required: false,
    default: 'https://firebasestorage.googleapis.com/v0/b/luyari-55dcd.appspot.com/o/elephantisland%2Fpages%2Fdefault-location.jpg?alt=media',
    validate: {
      validator: function(v) {
        if (!v) return true; // Allow empty values
        return v.includes('firebasestorage.googleapis.com') || v.startsWith('/uploads/');
      },
      message: 'Image must be a valid Firebase Storage URL or local path'
    }
  },
  url: {
    type: String,
    required: true,
    validate: {
      validator: function(v) {
        try {
          new URL(v);
          return true;
        } catch {
          return false;
        }
      },
      message: 'URL must be a valid URL format'
    }
  }
}, { _id: false });

// Main Page schema with 4 object fields
const PageSchema = new Schema({
  island: {
    type: IslandPageSchema,
    default: null
  },
  experiences: {
    type: ExperiencesPageSchema,
    default: null
  },
  testimonials: {
    type: TestimonialsPageSchema,
    default: null
  },
  locationAndcontacts: {
    type: LocationAndContactsPageSchema,
    default: null
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better query performance
PageSchema.index({ createdAt: -1 });

// Static method to initialize default pages
PageSchema.statics.initializeDefaultPages = async function() {
  const existingPage = await this.findOne();
  if (!existingPage) {
    const defaultData = {
      island: {
        title: 'Welcome to The Island',
        body1: 'Discover the beauty and wonder of our pristine island paradise.',
        body2: 'Experience the untouched natural beauty and unique wildlife that makes our island special.'
      },
      experiences: {
        title: 'Unforgettable Experiences',
        body1: 'Create memories that will last a lifetime with our curated experiences.',
        body2: 'From wildlife encounters to cultural immersion, every moment is designed to inspire.'
      },
      testimonials: {
        testimonials: []
      },
      locationAndcontacts: {
        title: 'Contact Information',
        body: 'Get in touch with us for more information about Elephant Island Lodge.',
        url: 'https://example.com',
        image: 'https://firebasestorage.googleapis.com/v0/b/luyari-55dcd.appspot.com/o/elephantisland%2Fpages%2Fdefault-location.jpg?alt=media'
      }
    };

    await this.create(defaultData);
  }
};

// Pre-save middleware for data validation and cleanup
PageSchema.pre('save', function(next) {
  // Clean up empty testimonials
  if (this.testimonials && this.testimonials.testimonials) {
    this.testimonials.testimonials = this.testimonials.testimonials.filter(testimonial =>
      testimonial.name && testimonial.comment
    );
  }

  next();
});

// Use existing model if it exists, otherwise create a new one
export const Page = mongoose.models.Page || mongoose.model('Page', PageSchema);
