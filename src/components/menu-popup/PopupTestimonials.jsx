import React from 'react'

export default function PopupTestimonials({data}) {
  console.log('PopupTestimonials:',data)
  return (
    <div className='popu-island-experiences text-white flex z-30 absolute top-0 left-0 w-full h-full bg-black/75 overflow-hidden overflow-y-auto'>
      <div className='flex flex-col mt-20 w-full h-fit items-center justify-start gap-5 overflow-y-auto'>
        {/* {data?.map(i=>
          <div key={i?.name} className='flex w-full flex-col h-fit gap-5'>
            <h1 className='w-full text-4xl text-left leading-12 uppercase'>
              {i?.name}
            </h1>
            <p className='w-full text-left leading-7'>
              ''{i?.comment}''
            </p>
          </div>
        )} */}
      </div>
    </div>
  )
}
