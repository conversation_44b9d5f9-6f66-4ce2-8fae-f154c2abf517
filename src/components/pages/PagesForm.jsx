'use client';

import React, { useState, useCallback, useMemo, useEffect } from 'react';
import ReactQuillWrapper from '@/components/common/ReactQuillWrapper';
import 'react-quill/dist/quill.snow.css';

// Quill editor configuration
const quillModules = {
  toolbar: [
    [{ 'header': [1, 2, 3, false] }],
    ['bold', 'italic', 'underline', 'strike'],
    [{ 'list': 'ordered'}, { 'list': 'bullet' }],
    [{ 'color': [] }, { 'background': [] }],
    [{ 'align': [] }],
    ['link'],
    ['clean']
  ],
};

const quillFormats = [
  'header', 'bold', 'italic', 'underline', 'strike',
  'list', 'bullet', 'color', 'background', 'align', 'link'
];

const PagesForm = React.memo(({ pages, onSave, onCancel, isLoading }) => {
  const [activeSection, setActiveSection] = useState('island');
  const [formData, setFormData] = useState({
    island: {
      title: '',
      body1: '',
      body2: ''
    },
    experiences: {
      title: '',
      body1: '',
      body2: ''
    },
    testimonials: {
      testimonials: []
    },
    locationAndcontacts: {
      title: '',
      body: '',
      image: '',
      url: ''
    }
  });

  const [errors, setErrors] = useState({});
  const [uploadingImage, setUploadingImage] = useState(false);
  const [testimonialFormData, setTestimonialFormData] = useState({
    name: '',
    comment: ''
  });

  // Initialize form data when pages prop changes
  useEffect(() => {
    if (pages) {
      setFormData({
        island: pages.island || {
          title: '',
          body1: '',
          body2: ''
        },
        experiences: pages.experiences || {
          title: '',
          body1: '',
          body2: ''
        },
        testimonials: pages.testimonials || {
          testimonials: []
        },
        locationAndcontacts: pages.locationAndcontacts || {
          title: '',
          body: '',
          image: '',
          url: ''
        }
      });
    }
  }, [pages]);

  // Validation rules
  const validateForm = useCallback(() => {
    const newErrors = {};

    // Validate island section
    if (!formData.island.title?.trim()) {
      newErrors['island.title'] = 'Island title is required';
    }
    if (!formData.island.body1?.trim()) {
      newErrors['island.body1'] = 'Island body1 is required';
    }
    if (!formData.island.body2?.trim()) {
      newErrors['island.body2'] = 'Island body2 is required';
    }

    // Validate experiences section
    if (!formData.experiences.title?.trim()) {
      newErrors['experiences.title'] = 'Experiences title is required';
    }
    if (!formData.experiences.body1?.trim()) {
      newErrors['experiences.body1'] = 'Experiences body1 is required';
    }
    if (!formData.experiences.body2?.trim()) {
      newErrors['experiences.body2'] = 'Experiences body2 is required';
    }

    // Validate location and contacts section
    if (!formData.locationAndcontacts.title?.trim()) {
      newErrors['locationAndcontacts.title'] = 'Location title is required';
    }
    if (!formData.locationAndcontacts.body?.trim()) {
      newErrors['locationAndcontacts.body'] = 'Location body is required';
    }
    if (!formData.locationAndcontacts.image?.trim()) {
      newErrors['locationAndcontacts.image'] = 'Location image is required';
    }
    if (!formData.locationAndcontacts.url?.trim()) {
      newErrors['locationAndcontacts.url'] = 'Location URL is required';
    } else {
      try {
        new URL(formData.locationAndcontacts.url);
      } catch {
        newErrors['locationAndcontacts.url'] = 'Please enter a valid URL';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData]);

  // Handle Quill editor content changes
  const handleQuillChange = useCallback((content, section, fieldName) => {
    setFormData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [fieldName]: content
      }
    }));

    // Clear error when user starts typing
    if (errors[`${section}.${fieldName}`]) {
      setErrors(prev => ({
        ...prev,
        [`${section}.${fieldName}`]: ''
      }));
    }
  }, [errors]);

  // Handle regular field changes
  const handleFieldChange = useCallback((section, field, value) => {
    setFormData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));

    // Clear error when user starts typing
    if (errors[`${section}.${field}`]) {
      setErrors(prev => ({
        ...prev,
        [`${section}.${field}`]: ''
      }));
    }
  }, [errors]);

  // Handle image upload
  const handleImageUpload = useCallback(async (file, section) => {
    setUploadingImage(true);

    try {
      const uploadFormData = new FormData();
      uploadFormData.append('files', file);

      const response = await fetch('/api/upload/pages', {
        method: 'POST',
        body: uploadFormData,
      });

      const result = await response.json();

      if (result.success && result.files?.[0]) {
        const imageUrl = result.files[0].url;
        handleFieldChange(section, 'image', imageUrl);
      } else {
        throw new Error(result.message || 'Upload failed');
      }
    } catch (error) {
      console.error('Upload error:', error);
      setErrors(prev => ({
        ...prev,
        [`${section}.image`]: 'Failed to upload image'
      }));
    } finally {
      setUploadingImage(false);
    }
  }, [handleFieldChange]);

  // Handle testimonial operations
  const handleAddTestimonial = useCallback(() => {
    if (!testimonialFormData.name || !testimonialFormData.comment) {
      alert('Please fill in both name and comment');
      return;
    }

    // Check for unique name
    const existingNames = formData.testimonials.testimonials.map(t => t.name.toLowerCase());
    if (existingNames.includes(testimonialFormData.name.toLowerCase())) {
      alert('Testimonial name must be unique');
      return;
    }

    setFormData(prev => ({
      ...prev,
      testimonials: {
        testimonials: [...prev.testimonials.testimonials, { ...testimonialFormData }]
      }
    }));

    setTestimonialFormData({ name: '', comment: '' });
  }, [testimonialFormData, formData.testimonials.testimonials]);

  const handleRemoveTestimonial = useCallback((index) => {
    setFormData(prev => ({
      ...prev,
      testimonials: {
        testimonials: prev.testimonials.testimonials.filter((_, i) => i !== index)
      }
    }));
  }, []);

  // Handle form submission
  const handleSubmit = useCallback(async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      await onSave(formData);
    } catch (error) {
      console.error('Save error:', error);
    }
  }, [formData, validateForm, onSave]);

  // Handle section save
  const handleSectionSave = useCallback(async (section) => {
    try {
      await onSave({ section, data: formData[section] });
    } catch (error) {
      console.error('Save error:', error);
    }
  }, [formData, onSave]);

  // Memoized section options
  const sectionOptions = useMemo(() => [
    { value: 'island', label: 'The Island' },
    { value: 'experiences', label: 'Experiences' },
    { value: 'testimonials', label: 'Testimonials' },
    { value: 'locationAndcontacts', label: 'Location & Contacts' }
  ], []);

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-trasandina-black text-gray-900 uppercase tracking-wide">
          Pages Management
        </h2>
        <div className="flex space-x-3">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancel
          </button>
          <button
            type="submit"
            form="pages-form"
            disabled={isLoading}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? 'Saving...' : 'Save All Pages'}
          </button>
        </div>
      </div>

      {/* Section Tabs */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          {sectionOptions.map((section) => (
            <button
              key={section.value}
              type="button"
              onClick={() => setActiveSection(section.value)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeSection === section.value
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {section.label}
            </button>
          ))}
        </nav>
      </div>

      <form id="pages-form" onSubmit={handleSubmit} className="space-y-6">
        {/* Island Section */}
        {activeSection === 'island' && (
          <IslandSection
            formData={formData.island}
            errors={errors}
            onQuillChange={handleQuillChange}
            onSectionSave={() => handleSectionSave('island')}
            isLoading={isLoading}
          />
        )}

        {/* Experiences Section */}
        {activeSection === 'experiences' && (
          <ExperiencesSection
            formData={formData.experiences}
            errors={errors}
            onQuillChange={handleQuillChange}
            onSectionSave={() => handleSectionSave('experiences')}
            isLoading={isLoading}
          />
        )}

        {/* Testimonials Section */}
        {activeSection === 'testimonials' && (
          <TestimonialsSection
            testimonials={formData.testimonials.testimonials}
            testimonialFormData={testimonialFormData}
            setTestimonialFormData={setTestimonialFormData}
            onAddTestimonial={handleAddTestimonial}
            onRemoveTestimonial={handleRemoveTestimonial}
            onSectionSave={() => handleSectionSave('testimonials')}
            isLoading={isLoading}
          />
        )}

        {/* Location and Contacts Section */}
        {activeSection === 'locationAndcontacts' && (
          <LocationAndContactsSection
            formData={formData.locationAndcontacts}
            errors={errors}
            onFieldChange={handleFieldChange}
            onImageUpload={handleImageUpload}
            uploadingImage={uploadingImage}
            onSectionSave={() => handleSectionSave('locationAndcontacts')}
            isLoading={isLoading}
          />
        )}
      </form>
    </div>
  );
});

// Island Section Component
const IslandSection = React.memo(({
  formData,
  errors,
  onQuillChange,
  onSectionSave,
  isLoading
}) => (
  <div className="space-y-6">
    <div className="flex items-center justify-between">
      <h3 className="text-lg font-medium text-gray-900">Island Page Content</h3>
      <button
        type="button"
        onClick={onSectionSave}
        disabled={isLoading}
        className="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {isLoading ? 'Saving...' : 'Save Island Section'}
      </button>
    </div>

    {/* Title */}
    <div>
      <label htmlFor="island-title" className="block text-sm font-medium text-gray-700 mb-2">
        Title *
      </label>
      <div className={`${errors['island.title'] ? 'border-red-500' : ''}`}>
        <ReactQuillWrapper
          theme="snow"
          value={formData.title}
          onChange={(content) => onQuillChange(content, 'island', 'title')}
          modules={quillModules}
          formats={quillFormats}
          placeholder="Enter island page title"
          style={{ minHeight: '80px' }}
          className={`border rounded-md ${errors['island.title'] ? 'border-red-500' : 'border-gray-300'}`}
        />
      </div>
      {errors['island.title'] && (
        <p className="mt-1 text-sm text-red-600">{errors['island.title']}</p>
      )}
    </div>

    {/* Body 1 */}
    <div>
      <label htmlFor="island-body1" className="block text-sm font-medium text-gray-700 mb-2">
        Body 1 *
      </label>
      <div className={`${errors['island.body1'] ? 'border-red-500' : ''}`}>
        <ReactQuillWrapper
          theme="snow"
          value={formData.body1}
          onChange={(content) => onQuillChange(content, 'island', 'body1')}
          modules={quillModules}
          formats={quillFormats}
          placeholder="Enter first body content"
          style={{ minHeight: '120px' }}
          className={`border rounded-md ${errors['island.body1'] ? 'border-red-500' : 'border-gray-300'}`}
        />
      </div>
      {errors['island.body1'] && (
        <p className="mt-1 text-sm text-red-600">{errors['island.body1']}</p>
      )}
    </div>

    {/* Body 2 */}
    <div>
      <label htmlFor="island-body2" className="block text-sm font-medium text-gray-700 mb-2">
        Body 2 *
      </label>
      <div className={`${errors['island.body2'] ? 'border-red-500' : ''}`}>
        <ReactQuillWrapper
          theme="snow"
          value={formData.body2}
          onChange={(content) => onQuillChange(content, 'island', 'body2')}
          modules={quillModules}
          formats={quillFormats}
          placeholder="Enter second body content"
          style={{ minHeight: '120px' }}
          className={`border rounded-md ${errors['island.body2'] ? 'border-red-500' : 'border-gray-300'}`}
        />
      </div>
      {errors['island.body2'] && (
        <p className="mt-1 text-sm text-red-600">{errors['island.body2']}</p>
      )}
    </div>
  </div>
));

// Experiences Section Component
const ExperiencesSection = React.memo(({
  formData,
  errors,
  onQuillChange,
  onSectionSave,
  isLoading
}) => (
  <div className="space-y-6">
    <div className="flex items-center justify-between">
      <h3 className="text-lg font-medium text-gray-900">Experiences Page Content</h3>
      <button
        type="button"
        onClick={onSectionSave}
        disabled={isLoading}
        className="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {isLoading ? 'Saving...' : 'Save Experiences Section'}
      </button>
    </div>

    {/* Title */}
    <div>
      <label htmlFor="experiences-title" className="block text-sm font-medium text-gray-700 mb-2">
        Title *
      </label>
      <div className={`${errors['experiences.title'] ? 'border-red-500' : ''}`}>
        <ReactQuillWrapper
          theme="snow"
          value={formData.title}
          onChange={(content) => onQuillChange(content, 'experiences', 'title')}
          modules={quillModules}
          formats={quillFormats}
          placeholder="Enter experiences page title"
          style={{ minHeight: '80px' }}
          className={`border rounded-md ${errors['experiences.title'] ? 'border-red-500' : 'border-gray-300'}`}
        />
      </div>
      {errors['experiences.title'] && (
        <p className="mt-1 text-sm text-red-600">{errors['experiences.title']}</p>
      )}
    </div>

    {/* Body 1 */}
    <div>
      <label htmlFor="experiences-body1" className="block text-sm font-medium text-gray-700 mb-2">
        Body 1 *
      </label>
      <div className={`${errors['experiences.body1'] ? 'border-red-500' : ''}`}>
        <ReactQuillWrapper
          theme="snow"
          value={formData.body1}
          onChange={(content) => onQuillChange(content, 'experiences', 'body1')}
          modules={quillModules}
          formats={quillFormats}
          placeholder="Enter first body content"
          style={{ minHeight: '120px' }}
          className={`border rounded-md ${errors['experiences.body1'] ? 'border-red-500' : 'border-gray-300'}`}
        />
      </div>
      {errors['experiences.body1'] && (
        <p className="mt-1 text-sm text-red-600">{errors['experiences.body1']}</p>
      )}
    </div>

    {/* Body 2 */}
    <div>
      <label htmlFor="experiences-body2" className="block text-sm font-medium text-gray-700 mb-2">
        Body 2 *
      </label>
      <div className={`${errors['experiences.body2'] ? 'border-red-500' : ''}`}>
        <ReactQuillWrapper
          theme="snow"
          value={formData.body2}
          onChange={(content) => onQuillChange(content, 'experiences', 'body2')}
          modules={quillModules}
          formats={quillFormats}
          placeholder="Enter second body content"
          style={{ minHeight: '120px' }}
          className={`border rounded-md ${errors['experiences.body2'] ? 'border-red-500' : 'border-gray-300'}`}
        />
      </div>
      {errors['experiences.body2'] && (
        <p className="mt-1 text-sm text-red-600">{errors['experiences.body2']}</p>
      )}
    </div>
  </div>
));
// Testimonials Section Component
const TestimonialsSection = React.memo(({
  testimonials,
  testimonialFormData,
  setTestimonialFormData,
  onAddTestimonial,
  onRemoveTestimonial,
  onSectionSave,
  isLoading
}) => (
  <div className="space-y-6">
    <div className="flex items-center justify-between">
      <h3 className="text-lg font-medium text-gray-900">Testimonials Management</h3>
      <button
        type="button"
        onClick={onSectionSave}
        disabled={isLoading}
        className="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {isLoading ? 'Saving...' : 'Save Testimonials Section'}
      </button>
    </div>

    {/* Add New Testimonial Form */}
    <div className="bg-gray-50 p-4 rounded-lg">
      <h4 className="text-md font-medium text-gray-800 mb-3">Add New Testimonial</h4>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Name *
          </label>
          <input
            type="text"
            value={testimonialFormData.name}
            onChange={(e) => setTestimonialFormData(prev => ({ ...prev, name: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter testimonial author name"
            maxLength={100}
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Comment *
          </label>
          <textarea
            value={testimonialFormData.comment}
            onChange={(e) => setTestimonialFormData(prev => ({ ...prev, comment: e.target.value }))}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter testimonial comment"
            maxLength={1000}
          />
        </div>
      </div>
      <button
        type="button"
        onClick={onAddTestimonial}
        disabled={!testimonialFormData.name || !testimonialFormData.comment}
        className="mt-3 px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        Add Testimonial
      </button>
    </div>

    {/* Existing Testimonials */}
    {testimonials.length > 0 && (
      <div>
        <h4 className="text-md font-medium text-gray-800 mb-3">
          Existing Testimonials ({testimonials.length})
        </h4>
        <div className="space-y-3">
          {testimonials.map((testimonial, index) => (
            <div key={index} className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <h5 className="font-medium text-gray-900">{testimonial.name}</h5>
                  <p className="text-gray-600 mt-1">{testimonial.comment}</p>
                </div>
                <button
                  type="button"
                  onClick={() => onRemoveTestimonial(index)}
                  className="ml-4 text-red-600 hover:text-red-800"
                >
                  Remove
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    )}
  </div>
));

// Location and Contacts Section Component
const LocationAndContactsSection = React.memo(({
  formData,
  errors,
  onFieldChange,
  onImageUpload,
  uploadingImage,
  onSectionSave,
  isLoading
}) => (
  <div className="space-y-6">
    <div className="flex items-center justify-between">
      <h3 className="text-lg font-medium text-gray-900">Location & Contacts Page Content</h3>
      <button
        type="button"
        onClick={onSectionSave}
        disabled={isLoading}
        className="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {isLoading ? 'Saving...' : 'Save Location Section'}
      </button>
    </div>

    {/* Title */}
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        Title *
      </label>
      <input
        type="text"
        value={formData.title}
        onChange={(e) => onFieldChange('locationAndcontacts', 'title', e.target.value)}
        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
        placeholder="Enter location page title"
        maxLength={200}
      />
      {errors['locationAndcontacts.title'] && (
        <p className="mt-1 text-sm text-red-600">{errors['locationAndcontacts.title']}</p>
      )}
    </div>

    {/* Body */}
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        Body Content *
      </label>
      <textarea
        value={formData.body}
        onChange={(e) => onFieldChange('locationAndcontacts', 'body', e.target.value)}
        rows={6}
        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
        placeholder="Enter location page content"
        maxLength={5000}
      />
      <div className="flex justify-between mt-1">
        {errors['locationAndcontacts.body'] && (
          <p className="text-sm text-red-600">{errors['locationAndcontacts.body']}</p>
        )}
        <p className="text-sm text-gray-500">
          {formData.body.length}/5000 characters
        </p>
      </div>
    </div>

    {/* Image */}
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        Main Image *
      </label>
      <div className="space-y-2">
        <input
          type="file"
          accept="image/*"
          onChange={(e) => e.target.files[0] && onImageUpload(e.target.files[0], 'locationAndcontacts')}
          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          disabled={uploadingImage}
        />
        {uploadingImage && (
          <p className="text-sm text-blue-600">Uploading image...</p>
        )}
        {formData.image && (
          <div className="mt-2">
            <img
              src={formData.image}
              alt="Preview"
              className="w-32 h-32 object-cover rounded-lg border border-gray-300"
            />
          </div>
        )}
      </div>
      {errors['locationAndcontacts.image'] && (
        <p className="mt-1 text-sm text-red-600">{errors['locationAndcontacts.image']}</p>
      )}
    </div>

    {/* URL */}
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        URL *
      </label>
      <input
        type="url"
        value={formData.url}
        onChange={(e) => onFieldChange('locationAndcontacts', 'url', e.target.value)}
        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
        placeholder="https://example.com"
      />
      {errors['locationAndcontacts.url'] && (
        <p className="mt-1 text-sm text-red-600">{errors['locationAndcontacts.url']}</p>
      )}
    </div>
  </div>
));

// Set display names
IslandSection.displayName = 'IslandSection';
ExperiencesSection.displayName = 'ExperiencesSection';
TestimonialsSection.displayName = 'TestimonialsSection';
LocationAndContactsSection.displayName = 'LocationAndContactsSection';
PagesForm.displayName = 'PagesForm';

export default PagesForm;
