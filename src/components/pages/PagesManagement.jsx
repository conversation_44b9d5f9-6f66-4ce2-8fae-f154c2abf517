'use client';

import React, { useState, useCallback } from 'react';
import PagesForm from './PagesForm';

const PagesManagement = React.memo(() => {
  const [currentView, setCurrentView] = useState('form'); // 'form' only now
  const [pages, setPages] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [notification, setNotification] = useState(null);

  // Show notification helper
  const showNotification = useCallback((message, type = 'success') => {
    setNotification({ message, type });
    setTimeout(() => setNotification(null), 5000);
  }, []);

  // Fetch pages data
  const fetchPages = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/pages');
      const data = await response.json();

      if (data.success) {
        setPages(data.data);
      } else {
        throw new Error(data.message || 'Failed to fetch pages');
      }
    } catch (error) {
      console.error('Error fetching pages:', error);
      showNotification('Failed to fetch pages: ' + error.message, 'error');
    } finally {
      setIsLoading(false);
    }
  }, [showNotification]);

  // Load pages on component mount
  React.useEffect(() => {
    fetchPages();
  }, [fetchPages]);

  // Handle save page (section or all)
  const handleSave = useCallback(async (formData) => {
    setIsLoading(true);
    try {
      let response;

      if (formData.section && formData.data) {
        // Save individual section
        response = await fetch('/api/pages', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(formData),
        });
      } else {
        // Save all sections
        response = await fetch('/api/pages', {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(formData),
        });
      }

      const data = await response.json();

      if (data.success) {
        showNotification(
          formData.section
            ? `${formData.section} section updated successfully`
            : 'All pages updated successfully'
        );
        // Refresh pages data
        await fetchPages();
      } else {
        throw new Error(data.message || 'Failed to save pages');
      }
    } catch (error) {
      console.error('Save error:', error);
      showNotification('Failed to save pages: ' + error.message, 'error');
    } finally {
      setIsLoading(false);
    }
  }, [showNotification, fetchPages]);

  // Handle cancel
  const handleCancel = useCallback(() => {
    // Refresh pages data to reset any unsaved changes
    fetchPages();
  }, [fetchPages]);

  // Handle reset all pages
  const handleResetAll = useCallback(async () => {
    if (!confirm('Are you sure you want to reset all pages to default values? This action cannot be undone.')) {
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch('/api/pages', {
        method: 'DELETE',
      });

      const data = await response.json();

      if (data.success) {
        showNotification('All pages reset to default values');
        setPages(data.data);
      } else {
        throw new Error(data.message || 'Failed to reset pages');
      }
    } catch (error) {
      console.error('Reset error:', error);
      showNotification('Failed to reset pages: ' + error.message, 'error');
    } finally {
      setIsLoading(false);
    }
  }, [showNotification]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="px-4 py-6 sm:px-0">
          <div className="border-b border-gray-200 pb-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-4xl font-trasandina-black leading-tight text-gray-900 uppercase tracking-wide">
                  Pages Management
                </h1>
                <p className="mt-3 text-lg font-trasandina-light text-gray-600 leading-relaxed">
                  Manage content for the four main navbar sections: The Island, Experiences, Testimonials, and Location & Contacts.
                </p>
              </div>
              
              <button
                onClick={handleResetAll}
                disabled={isLoading}
                className="px-6 py-3 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Reset All Pages
              </button>
            </div>
          </div>
        </div>

        {/* Notification */}
        {notification && (
          <div className={`mb-6 p-4 rounded-md ${
            notification.type === 'success' 
              ? 'bg-green-50 border border-green-200 text-green-700'
              : 'bg-red-50 border border-red-200 text-red-700'
          }`}>
            <div className="flex items-center justify-between">
              <span>{notification.message}</span>
              <button
                onClick={() => setNotification(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                ×
              </button>
            </div>
          </div>
        )}

        {/* Loading Overlay */}
        {isLoading && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 flex items-center space-x-3">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              <span className="text-gray-700">Processing...</span>
            </div>
          </div>
        )}

        {/* Content */}
        <div className="px-4 sm:px-0">
          <PagesForm
            pages={pages}
            onSave={handleSave}
            onCancel={handleCancel}
            isLoading={isLoading}
          />
        </div>

        {/* Footer Info */}
        <div className="mt-8 px-4 sm:px-0">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="text-sm font-medium text-blue-800 mb-2">
              Pages Management Information
            </h3>
            <div className="text-sm text-blue-700 space-y-1">
              <p>• <strong>Island & Experiences:</strong> Rich text content with title, body1, and body2 fields</p>
              <p>• <strong>Testimonials:</strong> Manage customer testimonials with unique names and comments</p>
              <p>• <strong>Location & Contacts:</strong> Contact information with title, body, image, and external URL</p>
              <p>• All images are stored in Firebase Storage under the 'elephantisland/pages/' folder</p>
              <p>• Use the tabs above to switch between different page sections for editing</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
});

PagesManagement.displayName = 'PagesManagement';

export default PagesManagement;
